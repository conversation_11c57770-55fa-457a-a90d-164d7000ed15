/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700&display=swap');
@import 'swiper/css';
@import '@fortawesome/fontawesome-free/css/all.min.css';


/* Global reset and box-sizing */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Base HTML and body styles */
html, body {
  margin: 0;
  padding: 0;
  width: 100vw;
  height: 100vh;
  background-color: var(--primary-bg, #f9f9f9);
  color: var(--text-color, #333);
  overflow-x: hidden; /* Prevent horizontal scrolling */
  scrollbar-width: none; /* Hide scrollbar for Firefox */
}

/* Hide scrollbar by default for Webkit browsers */
::-webkit-scrollbar {
  display: none;
}

/* Only show scrollbar when content actually overflows and has the appropriate class */
.scrollable {
  scrollbar-width: thin; /* For Firefox */
}

.scrollable::-webkit-scrollbar {
  display: block;
  width: 8px;
  background-color: transparent;
}

.scrollable::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

/* Only show scrollbar when hovering over the scrollable area */
.scrollable:not(:hover)::-webkit-scrollbar-thumb {
  background-color: transparent;
}

body {
  font-family: 'Roboto', 'Helvetica Neue', sans-serif;
  font-size: 14px;
  line-height: 1.5;
  overflow-y: auto; /* Enable vertical scrolling only when needed */
}

/* Card styles */
.card {
  background: var(--card-bg, white);
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 24px;
  border: 1px solid var(--border-color, #e0e0e0);
}

/* Scope .container to non-layout routes (e.g., login/signup) */
:where(:not([routerlink="/dashboard"], [routerlink="/analytics"])) .container {
  display: flex;
  width: 100vw;
  min-height: 100vh;
  max-width: none;
  font-family: Arial, sans-serif;
}

.form-section,
.left-section {
  flex: 1;
  padding: 40px;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.right-section {
  flex: 1;
  background: linear-gradient(135deg, #6b48ff, #a855f7);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 40px;
  color: white;
  min-height: 100vh;
  border-bottom-left-radius: 50px;
  position: relative;
  z-index: 1;
}

h1 {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 10px;
}

p {
  margin-bottom: 20px;
  color: #666;
  font-size: 0.9rem;
}

p a {
  color: #6b48ff;
  text-decoration: none;
}

form {
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: 100%;
  max-width: 400px;
}

.name-fields {
  display: flex;
  gap: 15px;
}

input {
  padding: 12px;
  border: 1px solid var(--input-border, #ccc);
  border-radius: 20px;
  font-size: 1rem;
  width: 100%;
  box-sizing: border-box;
  background-color: var(--input-bg, white);
  color: var(--input-text, #333);
}

input::placeholder {
  color: var(--placeholder-color, #757575);
}

select {
  padding: 12px;
  border: 1px solid var(--input-border, #ccc);
  border-radius: 20px;
  font-size: 1rem;
  width: 100%;
  box-sizing: border-box;
  background-color: var(--input-bg, white);
  color: var(--input-text, #333);
  appearance: auto;
}

.password-field {
  position: relative;
}

.toggle-password {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: #666;
  font-size: 1rem;
}

.forgot-password {
  font-size: 0.9rem;
  color: #6b48ff;
  text-decoration: none;
  align-self: flex-start;
  margin-top: -10px;
}

.forgot-password:hover {
  text-decoration: underline;
}

.terms {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 0.9rem;
  color: #666;
}

.terms label a {
  color: #6b48ff;
  text-decoration: none;
}

.create-account-btn {
  padding: 12px;
  background: #6b48ff;
  color: white;
  border: none;
  border-radius: 20px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  text-transform: uppercase;
  transition: background 0.3s;
  width: 100%;
}

.create-account-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.create-account-btn:hover:not(:disabled) {
  backgroundQD: #5a3de6;
}

.divider {
  text-align: center;
  margin: 20px 0;
  color: #666;
  font-size: 0.9rem;
  position: relative;
  width: 100%;
  max-width: 400px;
}

.divider::before,
.divider::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 40%;
  height: 1px;
  background: #ccc;
}

.divider::before {
  left: 0;
}

.divider::after {
  right: 0;
}

.third-party {
  padding: 8px 16px;
  background: white;
  color: black;
  border: 1px solid #ccc;
  border-radius: 20px;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  width: 100%;
  max-width: 400px;
  height: 40px;
  transition: background-color 0.2s, border-color 0.2s;
  margin-bottom: 10px;
}

.third-party:hover {
  background-color: #f5f5f5;
  border-color: #aaa;
}

.third-party i {
  margin-right: 8px;
  font-size: 18px;
}

/* Google multi-colored icon */
.google i.fa-google {
  /* Fallback color */
  color: #4285F4;
}

/* Apply multi-color effect for browsers that support it */
@supports (background-clip: text) or (-webkit-background-clip: text) {
  .google i.fa-google {
    background: conic-gradient(from -45deg, #ea4335 110deg, #4285f4 90deg 180deg, #34a853 180deg 270deg, #fbbc05 270deg) 73% 55%/150% 150% no-repeat;
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    -webkit-text-fill-color: transparent;
  }
}

.apple i {
  color: #000;
}

.error-message {
  color: red;
  margin-top: 10px;
  font-size: 0.9rem;
  text-align: center;
}

footer {
  margin-top: 20px;
  color: #666;
  font-size: 0.8rem;
  text-align: center;
}

.right-section h2 {
  font-size: 3.5rem;
  font-weight: bold;
  letter-spacing: 2px;
  margin: 0;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 2;
}

.right-section nav {
  display: flex;
  gap: 20px;
  justify-content: flex-start;
  margin-top: auto;
}

.right-section nav a {
  color: white;
  text-decoration: none;
  font-size: 0.9rem;
  padding: 5px 10px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  transition: background 0.3s;
}

.right-section nav a:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* Avatar styles */
.avatar-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e0e0e0;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  min-width: 20px;
  min-height: 20px;
}

.avatar-icon {
  width: 66.67%;
  height: 66.67%;
  color: #9ca3af;
  min-width: 12px;
  min-height: 12px;
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.scale-in {
  animation: scaleIn 0.3s ease-in-out;
}

@keyframes scaleIn {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

/* Theme variables */
:root {
  --primary-bg: #ffffff;
  --secondary-bg: #f5f5f5;
  --text-color: #333333;
  --border-color: #e0e0e0;
  --card-bg: #ffffff;
  --hover-bg: #f0f0f0;
  --input-bg: #ffffff;
  --input-text: #333333;
  --input-border: #cccccc;
  --placeholder-color: #757575;
}

/* Dark theme */
[data-theme="dark"] {
  --primary-bg: #1a1a1a;
  --secondary-bg: #2d2d2d;
  --text-color: #ffffff;
  --border-color: #404040;
  --card-bg: #2d2d2d;
  --hover-bg: #404040;
  --input-bg: #333333;
  --input-text: #ffffff;
  --input-border: #555555;
  --placeholder-color: #aaaaaa;
  --scrollbar-thumb-color: rgba(255, 255, 255, 0.2);
}

/* Dark mode scrollbar styles */
[data-theme="dark"] .scrollable::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .scrollable:not(:hover)::-webkit-scrollbar-thumb {
  background-color: transparent;
}

/* Exclude login and register pages from dark mode */
[data-theme="dark"] .light-mode-only,
[data-theme="dark"] .light-mode-only * {
  --text-color: #333333;
  --input-bg: #ffffff;
  --input-text: #333333;
  --input-border: #cccccc;
  --placeholder-color: #757575;
}

/* Ensure bottom-nav is always visible regardless of theme */
[data-theme="dark"] .bottom-nav,
[data-theme="light"] .bottom-nav,
.bottom-nav {
  background: rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(10px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

[data-theme="dark"] .bottom-nav .nav-link,
[data-theme="light"] .bottom-nav .nav-link,
.bottom-nav .nav-link {
  color: #ffffff !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
  opacity: 1 !important;
  visibility: visible !important;
}

[data-theme="dark"] .bottom-nav .nav-link:hover,
[data-theme="light"] .bottom-nav .nav-link:hover,
.bottom-nav .nav-link:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7) !important;
}

/* Fix for Angular form classes in dark mode for login and register pages */
[data-theme="dark"] .light-mode-only .ng-untouched,
[data-theme="dark"] .light-mode-only .ng-pristine,
[data-theme="dark"] .light-mode-only .ng-invalid,
[data-theme="dark"] .light-mode-only .ng-valid,
[data-theme="dark"] .light-mode-only input,
[data-theme="dark"] .light-mode-only select,
[data-theme="dark"] .light-mode-only option,
[data-theme="dark"] .light-mode-only h1,
[data-theme="dark"] .light-mode-only p,
[data-theme="dark"] .light-mode-only label,
[data-theme="dark"] .light-mode-only a {
  background-color: #ffffff !important;
  color: #333333 !important;
  border-color: #cccccc !important;
}

/* Specific overrides for form elements */
[data-theme="dark"] .light-mode-only form input,
[data-theme="dark"] .light-mode-only form select,
[data-theme="dark"] .light-mode-only form textarea {
  background-color: #ffffff !important;
  color: #333333 !important;
  border: 1px solid #cccccc !important;
}

/* Specific overrides for Angular form classes */
[data-theme="dark"] app-login input.ng-untouched,
[data-theme="dark"] app-login input.ng-pristine,
[data-theme="dark"] app-login input.ng-invalid,
[data-theme="dark"] app-login input.ng-valid,
[data-theme="dark"] app-register-personnel-info input.ng-untouched,
[data-theme="dark"] app-register-personnel-info input.ng-pristine,
[data-theme="dark"] app-register-personnel-info input.ng-invalid,
[data-theme="dark"] app-register-personnel-info input.ng-valid,
[data-theme="dark"] app-register-personnel-info select.ng-untouched,
[data-theme="dark"] app-register-personnel-info select.ng-pristine,
[data-theme="dark"] app-register-personnel-info select.ng-invalid,
[data-theme="dark"] app-register-personnel-info select.ng-valid {
  background-color: #ffffff !important;
  color: #333333 !important;
  border-color: #cccccc !important;
}

/* Apply theme variables */
.dashboard-container,
.analytics-container {
  background-color: var(--primary-bg);
  color: var(--text-color);
}

/* Override global chart styles */
canvas {
  max-width: 100% !important;
  height: auto !important;
}

/* Responsive styles */
@media (min-width: 992px) {
  .card {
    padding: 32px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  }

  .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 20px rgba(0, 0, 0, 0.12);
  }
}

@media (max-width: 768px) {
  :where(:not([routerlink="/dashboard"], [routerlink="/analytics"])) .container {
    flex-direction: column;
  }
  .form-section,
  .left-section,
  .right-section {
    flex: 1;
    min-height: 50vh;
  }
  .right-section {
    border-bottom-left-radius: 0;
    border-top-right-radius: 50px;
  }
  .form-section,
  .left-section {
    padding: 20px;
  }
  form {
    max-width: 100%;
  }
  .avatar-placeholder {
    width: 36px;
    height: 36px;
  }

  .avatar-icon {
    min-width: 18px;
    min-height: 18px;
  }

  /* Fix for shopper-dashboard mobile layout */
  app-shopper-dashboard {
    margin-left: 0 !important;
    width: 100% !important;
    max-width: 100vw !important;
  }
}

@media (max-width: 480px) {
  .avatar-placeholder {
    width: 32px;
    height: 32px;
  }

  .avatar-icon {
    min-width: 16px;
    min-height: 16px;
  }
}

html, body { height: 100%; }
body { margin: 0; font-family: Roboto, "Helvetica Neue", sans-serif; }