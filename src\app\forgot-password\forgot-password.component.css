/* Reset default styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  .container {
    display: flex;
    width: 100vw;
    height: 100vh;
    min-height: 100vh;
    margin: 0;
    padding: 0;
    overflow: hidden;
    font-family: Arial, sans-serif;
  }

  .form-section {
    flex: 1;
    padding: 40px;
    background: #f5f5f5;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    overflow: hidden; /* Ensure stars don't overflow */
  }

  /* Glowing stars background */
  .form-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"><circle cx="10%" cy="10%" r="2" fill="white" opacity="0.5"><animate attributeName="opacity" values="0.5;1;0.5" dur="2s" repeatCount="indefinite"/></circle><circle cx="20%" cy="80%" r="3" fill="white" opacity="0.5"><animate attributeName="opacity" values="0.5;1;0.5" dur="3s" repeatCount="indefinite"/></circle><circle cx="50%" cy="30%" r="2" fill="white" opacity="0.5"><animate attributeName="opacity" values="0.5;1;0.5" dur="2.5s" repeatCount="indefinite"/></circle><circle cx="70%" cy="60%" r="3" fill="white" opacity="0.5"><animate attributeName="opacity" values="0.5;1;0.5" dur="3.5s" repeatCount="indefinite"/></circle><circle cx="90%" cy="20%" r="2" fill="white" opacity="0.5"><animate attributeName="opacity" values="0.5;1;0.5" dur="2s" repeatCount="indefinite"/></circle></svg>');
    pointer-events: none; /* Ensure stars don't interfere with clicks */
  }



  .back-link {
    position: absolute;
    top: 20px;
    left: 20px;
    color: #666;
    text-decoration: none;
    font-size: 0.9rem;
    cursor: pointer;
    transition: color 0.3s ease;
  }

  .back-link:hover {
    color: #6b48ff;
    text-decoration: underline;
  }

  h1 {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 10px;
  }

  p {
    margin-bottom: 20px;
    color: #666;
    font-size: 0.9rem;
    text-align: center;
    max-width: 400px;
  }

  form {
    display: flex;
    flex-direction: column;
    gap: 15px;
    width: 100%;
    max-width: 400px;
  }

  input {
    padding: 12px;
    border: 1px solid #ccc;
    border-radius: 20px;
    font-size: 1rem;
    width: 100%;
    box-sizing: border-box;
  }

  .create-account-btn {
    padding: 12px;
    background: #6b48ff;
    color: white;
    border: none;
    border-radius: 20px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    text-transform: uppercase;
    transition: background 0.3s;
    width: 100%;
  }

  .create-account-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
  }

  .create-account-btn:hover:not(:disabled) {
    background: #5a3de6;
  }

  .back-to-login-btn {
    padding: 12px;
    background: white;
    color: #666;
    border: 1px solid #ccc;
    border-radius: 20px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    text-transform: uppercase;
    transition: background 0.3s;
    width: 100%;
    max-width: 400px;
    text-align: center;
    text-decoration: none;
  }

  .back-to-login-btn:hover {
    background: #f0f0f0;
  }

  .error-message {
    color: red;
    margin-top: 10px;
    font-size: 0.9rem;
    text-align: center;
  }

  footer {
    margin-top: 20px;
    color: #666;
    font-size: 0.8rem;
  }

  /* Logo styling - covers entire right section */
  .right-section {
    flex: 1 !important;
    background: none !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    align-items: center !important;
    padding: 0 !important;
    margin: 0 !important;
    border-radius: 0 !important;
    position: relative !important;
    overflow: hidden !important;
    height: 100vh !important;
    min-height: 100vh !important;
    max-height: 100vh !important;
    width: 50vw !important;
    flex-shrink: 0 !important;
    box-sizing: border-box !important;
  }

  .receeto-logo {
    width: 100% !important;
    height: 100% !important;
    background-image: url('/assets/images/Recetto-Login_Register-Logo.png');
    background-size: 100% 100% !important;
    background-position: center center !important;
    background-repeat: no-repeat !important;
    display: block !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    z-index: 1 !important;
    box-sizing: border-box !important;
  }

  /* Bottom navigation bar */
  .bottom-nav {
    position: absolute !important;
    bottom: 20px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    display: flex !important;
    gap: 30px !important;
    z-index: 2 !important;
    background: rgba(0, 0, 0, 0.3) !important;
    backdrop-filter: blur(10px) !important;
    padding: 10px 20px !important;
    border-radius: 25px !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  }

  .bottom-nav .nav-link {
    color: #ffffff !important;
    text-decoration: none !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    padding: 5px 10px !important;
    border-radius: 15px !important;
    transition: all 0.3s ease !important;
    white-space: nowrap !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
    opacity: 1 !important;
    visibility: visible !important;
  }

  .bottom-nav .nav-link:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    transform: translateY(-2px) !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7) !important;
  }

  /* Mobile responsive styles for bottom nav */
  @media (max-width: 768px) {
    .bottom-nav {
      display: none !important;
    }
  }

  @media (max-width: 480px) {
    .bottom-nav {
      display: none !important;
    }
  }

/* Dark mode overrides - ensure forgot password page stays in light mode */
:host {
  /* Force light mode styles regardless of theme */
  color: #333 !important;
}

.container {
  background-color: #ffffff !important;
}

.form-section {
  background-color: #f5f5f5 !important;
  color: #333333 !important;
}

.form-section h1 {
  color: #333333 !important;
}

.form-section p {
  color: #666666 !important;
}

.form-section input {
  background-color: #ffffff !important;
  color: #333333 !important;
  border: 1px solid #ccc !important;
}

.form-section input::placeholder {
  color: #999999 !important;
}

.form-section .back-link {
  color: #666666 !important;
}

.form-section .back-to-login-btn {
  background-color: #ffffff !important;
  color: #666666 !important;
  border: 1px solid #ccc !important;
}

.form-section footer {
  color: #666666 !important;
}

/* Fix for Angular form classes in dark mode */
.ng-untouched, .ng-pristine, .ng-invalid, .ng-valid {
  background-color: #ffffff !important;
  color: #333333 !important;
}

